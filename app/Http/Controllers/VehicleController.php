<?php

namespace App\Http\Controllers;

use App\Models\Vehicle;
use App\Models\Group;
use App\Rules\ValidVin;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class VehicleController extends Controller
{
    public function __construct()
    {
        // Middleware се прилага в рутовете
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): View
    {
        $query = Vehicle::query();

        // Филтър по тип
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Филтър по статус
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        } else {
            // По подразбиране показваме само активните
            $query->where('is_active', true);
        }

        // Филтър по марка
        if ($request->filled('make')) {
            $query->where('make', 'like', '%' . $request->make . '%');
        }

        // Търсене по регистрационен номер или VIN
        if ($request->filled('search')) {
            $query->where(function($q) use ($request) {
                $q->where('registration_number', 'like', '%' . $request->search . '%')
                  ->orWhere('vin', 'like', '%' . $request->search . '%');
            });
        }

        $vehicles = $query->orderBy('created_at', 'desc')->get();
        $vehicleTypes = Vehicle::getVehicleTypes();
        $makes = Vehicle::whereNotNull('make')->distinct()->pluck('make')->sort();

        return view('vehicles.index', compact('vehicles', 'vehicleTypes', 'makes'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        $vehicleTypes = Vehicle::getVehicleTypes();
        $groups = Group::orderBy('name')->get();
        return view('vehicles.create', compact('vehicleTypes', 'groups'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'type' => 'required|in:' . implode(',', array_keys(Vehicle::getVehicleTypes())),
            'registration_number' => 'required|string|max:255|unique:vehicles',
            'vin' => ['required', 'string', 'max:255', 'unique:vehicles', new ValidVin()],
            'registration_date' => 'required|date',
            'initial_mileage' => 'required|integer|min:0',
            'make' => 'nullable|string|max:255',
            'model' => 'nullable|string|max:255',
            'year' => 'nullable|integer|min:1900|max:' . (date('Y') + 1),
            'color' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
            'group_id' => 'nullable|exists:groups,id',
        ]);

        $validated['current_mileage'] = $validated['initial_mileage'];
        $validated['user_id'] = auth()->id();

        Vehicle::create($validated);

        return redirect()->route('vehicles.index')
            ->with('success', 'Превозното средство е създадено успешно.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Vehicle $vehicle): View
    {
        $vehicle->load(['user', 'group.owner']);
        return view('vehicles.show', compact('vehicle'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Vehicle $vehicle): View
    {
        $vehicleTypes = Vehicle::getVehicleTypes();
        $groups = Group::orderBy('name')->get();
        return view('vehicles.edit', compact('vehicle', 'vehicleTypes', 'groups'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Vehicle $vehicle): RedirectResponse
    {
        $validated = $request->validate([
            'type' => 'required|in:' . implode(',', array_keys(Vehicle::getVehicleTypes())),
            'registration_number' => 'required|string|max:255|unique:vehicles,registration_number,' . $vehicle->id,
            'vin' => ['required', 'string', 'max:255', 'unique:vehicles,vin,' . $vehicle->id, new ValidVin()],
            'registration_date' => 'required|date',
            'current_mileage' => 'required|integer|min:' . $vehicle->initial_mileage,
            'make' => 'nullable|string|max:255',
            'model' => 'nullable|string|max:255',
            'year' => 'nullable|integer|min:1900|max:' . (date('Y') + 1),
            'color' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
            'is_active' => 'boolean',
            'group_id' => 'nullable|exists:groups,id',
        ]);

        $vehicle->update($validated);

        return redirect()->route('vehicles.index')
            ->with('success', 'Превозното средство е обновено успешно.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Vehicle $vehicle): RedirectResponse
    {
        $vehicle->delete();

        return redirect()->route('vehicles.index')
            ->with('success', 'Превозното средство е изтрито успешно.');
    }
}
