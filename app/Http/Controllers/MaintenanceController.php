<?php

namespace App\Http\Controllers;

use App\Models\Maintenance;
use App\Models\Vehicle;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class MaintenanceController extends Controller
{
    public function __construct()
    {
        // Middleware се прилага в рутовете
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): View
    {
        $user = $request->user();

        // Започваме с базовия query за обслужвания
        $query = Maintenance::with('vehicle.group');

        // Филтрираме обслужванията според достъпа до превозните средства
        if (!$user->hasRole('admin')) {
            $query->whereHas('vehicle', function ($vehicleQuery) use ($user) {
                $vehicleQuery->where(function ($q) use ($user) {
                    // Лични превозни средства
                    $q->where(function ($subQ) use ($user) {
                        $subQ->where('user_id', $user->id)
                             ->whereNull('group_id');
                    })
                    // Превозни средства от групи, където потребителят е собственик
                    ->orWhereHas('group', function ($groupQuery) use ($user) {
                        $groupQuery->where('owner_id', $user->id);
                    })
                    // Превозни средства от групи, където потребителят е мениджър
                    ->orWhereHas('group.managers', function ($managerQuery) use ($user) {
                        $managerQuery->where('user_id', $user->id);
                    })
                    // Превозни средства от групи, където потребителят е член
                    ->orWhereHas('group.members', function ($memberQuery) use ($user) {
                        $memberQuery->where('user_id', $user->id);
                    });
                });
            });
        }

        // Филтър по превозно средство
        if ($request->filled('vehicle_id')) {
            $query->where('vehicle_id', $request->vehicle_id);
        }

        // Филтър по тип
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        $maintenances = $query->orderBy('date', 'desc')->get();

        // Филтрираме превозните средства според достъпа
        if ($user->hasRole('admin')) {
            $vehicles = Vehicle::active()->orderBy('registration_number')->get();
        } else {
            $vehicles = Vehicle::active()
                ->where(function ($q) use ($user) {
                    // Лични превозни средства
                    $q->where(function ($subQ) use ($user) {
                        $subQ->where('user_id', $user->id)
                             ->whereNull('group_id');
                    })
                    // Превозни средства от групи, където потребителят е собственик
                    ->orWhereHas('group', function ($groupQuery) use ($user) {
                        $groupQuery->where('owner_id', $user->id);
                    })
                    // Превозни средства от групи, където потребителят е мениджър
                    ->orWhereHas('group.managers', function ($managerQuery) use ($user) {
                        $managerQuery->where('user_id', $user->id);
                    })
                    // Превозни средства от групи, където потребителят е член
                    ->orWhereHas('group.members', function ($memberQuery) use ($user) {
                        $memberQuery->where('user_id', $user->id);
                    });
                })
                ->orderBy('registration_number')
                ->get();
        }
        $maintenanceTypes = Maintenance::getMaintenanceTypes();

        return view('maintenances.index', compact('maintenances', 'vehicles', 'maintenanceTypes'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request): View
    {
        $user = $request->user();

        // Филтрираме превозните средства според достъпа
        if ($user->hasRole('admin')) {
            $vehicles = Vehicle::active()->orderBy('registration_number')->get();
        } else {
            $vehicles = Vehicle::active()
                ->where(function ($q) use ($user) {
                    // Лични превозни средства
                    $q->where(function ($subQ) use ($user) {
                        $subQ->where('user_id', $user->id)
                             ->whereNull('group_id');
                    })
                    // Превозни средства от групи, където потребителят е собственик
                    ->orWhereHas('group', function ($groupQuery) use ($user) {
                        $groupQuery->where('owner_id', $user->id);
                    })
                    // Превозни средства от групи, където потребителят е мениджър
                    ->orWhereHas('group.managers', function ($managerQuery) use ($user) {
                        $managerQuery->where('user_id', $user->id);
                    })
                    // Превозни средства от групи, където потребителят е член с regular_user+ роля
                    ->orWhereHas('group.members', function ($memberQuery) use ($user) {
                        $memberQuery->where('user_id', $user->id);
                    });
                })
                ->orderBy('registration_number')
                ->get();

            // Филтрираме според правата за добавяне на обслужвания
            $vehicles = $vehicles->filter(function ($vehicle) use ($user) {
                return $user->hasVehicleAccess($vehicle, 'maintenance_create');
            });
        }

        $maintenanceTypes = Maintenance::getMaintenanceTypes();
        $selectedVehicle = $request->get('vehicle_id');

        return view('maintenances.create', compact('vehicles', 'maintenanceTypes', 'selectedVehicle'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'vehicle_id' => 'required|exists:vehicles,id',
            'type' => 'required|in:' . implode(',', array_keys(Maintenance::getMaintenanceTypes())),
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'date' => 'required|date',
            'mileage' => 'nullable|integer|min:0',
            'cost' => 'nullable|numeric|min:0',
            'service_provider' => 'nullable|string|max:255',
            'next_service_date' => 'nullable|date|after:date',
            'next_service_mileage' => 'nullable|integer|min:0',
            'notes' => 'nullable|string',
        ]);

        // Проверяваме достъпа до превозното средство
        $vehicle = Vehicle::findOrFail($validated['vehicle_id']);
        $user = $request->user();

        if (!$user->hasVehicleAccess($vehicle, 'maintenance_create')) {
            abort(403, 'Нямате права да добавяте обслужвания за това превозно средство.');
        }

        Maintenance::create($validated);

        return redirect()->route('maintenances.index')
            ->with('success', 'Обслужването е записано успешно.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Maintenance $maintenance): View
    {
        $maintenance->load('vehicle');
        return view('maintenances.show', compact('maintenance'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Maintenance $maintenance): View
    {
        $vehicles = Vehicle::active()->orderBy('registration_number')->get();
        $maintenanceTypes = Maintenance::getMaintenanceTypes();

        return view('maintenances.edit', compact('maintenance', 'vehicles', 'maintenanceTypes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Maintenance $maintenance): RedirectResponse
    {
        $validated = $request->validate([
            'vehicle_id' => 'required|exists:vehicles,id',
            'type' => 'required|in:' . implode(',', array_keys(Maintenance::getMaintenanceTypes())),
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'date' => 'required|date',
            'mileage' => 'nullable|integer|min:0',
            'cost' => 'nullable|numeric|min:0',
            'service_provider' => 'nullable|string|max:255',
            'next_service_date' => 'nullable|date|after:date',
            'next_service_mileage' => 'nullable|integer|min:0',
            'notes' => 'nullable|string',
        ]);

        $maintenance->update($validated);

        return redirect()->route('maintenances.index')
            ->with('success', 'Обслужването е обновено успешно.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Maintenance $maintenance): RedirectResponse
    {
        $maintenance->delete();

        return redirect()->route('maintenances.index')
            ->with('success', 'Обслужването е изтрито успешно.');
    }
}
