@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-8">
    <x-breadcrumbs :items="[
        ['title' => 'Начало', 'url' => route('dashboard')],
        ['title' => 'Обслужвания', 'url' => route('maintenances.index')],
        ['title' => $maintenance->title]
    ]" />

    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-gray-800">{{ $maintenance->title }}</h1>
            <div class="space-x-2">
                <a href="{{ route('maintenances.edit', $maintenance) }}" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Редактирай
                </a>
                <a href="{{ route('maintenances.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Назад
                </a>
            </div>
        </div>

        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <div class="px-6 py-4 bg-gray-50 border-b">
                <h2 class="text-xl font-semibold text-gray-800">Детайли за обслужването</h2>
            </div>

            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Превозно средство -->
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-2">Превозно средство</label>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <a href="{{ route('vehicles.show', $maintenance->vehicle) }}" class="text-blue-600 hover:text-blue-900 font-medium">
                                {{ $maintenance->vehicle->full_name }}
                            </a>
                        </div>
                    </div>

                    <!-- Тип обслужване -->
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-2">Тип обслужване</label>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ $maintenance->type_name }}
                            </span>
                        </div>
                    </div>

                    <!-- Дата -->
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-2">Дата</label>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-gray-900">{{ $maintenance->date->format('d.m.Y') }}</p>
                        </div>
                    </div>

                    <!-- Километраж -->
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-2">Километраж</label>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-gray-900">
                                {{ $maintenance->mileage ? number_format($maintenance->mileage) . ' км' : 'Не е посочен' }}
                            </p>
                        </div>
                    </div>

                    <!-- Цена -->
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-2">Цена</label>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-gray-900">
                                {{ $maintenance->cost ? number_format($maintenance->cost, 2) . ' лв.' : 'Не е посочена' }}
                            </p>
                        </div>
                    </div>

                    <!-- Доставчик на услугата -->
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-2">Доставчик на услугата</label>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-gray-900">
                                {{ $maintenance->service_provider ?: 'Не е посочен' }}
                            </p>
                        </div>
                    </div>

                    <!-- Следващо обслужване - дата -->
                    @if($maintenance->next_service_date)
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-2">Следващо обслужване - дата</label>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-gray-900">{{ $maintenance->next_service_date->format('d.m.Y') }}</p>
                            @if($maintenance->next_service_date < now())
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 mt-1">
                                    Просрочено
                                </span>
                            @elseif($maintenance->next_service_date <= now()->addDays(30))
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 mt-1">
                                    Предстои
                                </span>
                            @endif
                        </div>
                    </div>
                    @endif

                    <!-- Следващо обслужване - километраж -->
                    @if($maintenance->next_service_mileage)
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-2">Следващо обслужване - километраж</label>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-gray-900">{{ number_format($maintenance->next_service_mileage) }} км</p>
                        </div>
                    </div>
                    @endif
                </div>

                <!-- Описание -->
                @if($maintenance->description)
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-500 mb-2">Описание</label>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-900 whitespace-pre-wrap">{{ $maintenance->description }}</p>
                    </div>
                </div>
                @endif

                <!-- Забележки -->
                @if($maintenance->notes)
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-500 mb-2">Забележки</label>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-gray-900 whitespace-pre-wrap">{{ $maintenance->notes }}</p>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Действия -->
        <div class="mt-6 flex justify-end space-x-4">
            <form action="{{ route('maintenances.destroy', $maintenance) }}" method="POST" class="inline">
                @csrf
                @method('DELETE')
                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded" onclick="return confirm('Сигурни ли сте, че искате да изтриете това обслужване?')">
                    Изтрий обслужването
                </button>
            </form>
        </div>
    </div>
</div>
@endsection
