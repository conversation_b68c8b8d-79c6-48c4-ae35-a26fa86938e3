@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-8">
    <x-breadcrumbs :items="[
        ['title' => 'Начало', 'url' => route('dashboard')],
        ['title' => 'Обслужвания', 'url' => route('maintenances.index')],
        ['title' => $maintenance->title, 'url' => route('maintenances.show', $maintenance)],
        ['title' => 'Редактирай']
    ]" />

    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-gray-800">Редактирай обслужване</h1>
            <a href="{{ route('maintenances.show', $maintenance) }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Назад
            </a>
        </div>

        <div class="bg-white shadow-md rounded-lg p-6">
            <form action="{{ route('maintenances.update', $maintenance) }}" method="POST">
                @csrf
                @method('PUT')

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Превозно средство -->
                    <div>
                        <label for="vehicle_id" class="block text-sm font-medium text-gray-700 mb-2">Превозно средство *</label>
                        <select name="vehicle_id" id="vehicle_id" required class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('vehicle_id') border-red-500 @enderror">
                            <option value="">Изберете превозно средство</option>
                            @foreach($vehicles as $vehicle)
                                <option value="{{ $vehicle->id }}" {{ old('vehicle_id', $maintenance->vehicle_id) == $vehicle->id ? 'selected' : '' }}>
                                    {{ $vehicle->full_name }}
                                </option>
                            @endforeach
                        </select>
                        @error('vehicle_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Тип обслужване -->
                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Тип обслужване *</label>
                        <select name="type" id="type" required class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('type') border-red-500 @enderror">
                            <option value="">Изберете тип</option>
                            @foreach($maintenanceTypes as $key => $name)
                                <option value="{{ $key }}" {{ old('type', $maintenance->type) == $key ? 'selected' : '' }}>
                                    {{ $name }}
                                </option>
                            @endforeach
                        </select>
                        @error('type')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Заглавие -->
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Заглавие *</label>
                        <input type="text" name="title" id="title" value="{{ old('title', $maintenance->title) }}" required 
                               class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('title') border-red-500 @enderror">
                        @error('title')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Дата -->
                    <div>
                        <label for="date" class="block text-sm font-medium text-gray-700 mb-2">Дата *</label>
                        <input type="date" name="date" id="date" value="{{ old('date', $maintenance->date->format('Y-m-d')) }}" required 
                               class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('date') border-red-500 @enderror">
                        @error('date')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Километраж -->
                    <div>
                        <label for="mileage" class="block text-sm font-medium text-gray-700 mb-2">Километраж</label>
                        <input type="number" name="mileage" id="mileage" value="{{ old('mileage', $maintenance->mileage) }}" min="0" 
                               class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('mileage') border-red-500 @enderror">
                        @error('mileage')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Цена -->
                    <div>
                        <label for="cost" class="block text-sm font-medium text-gray-700 mb-2">Цена (лв.)</label>
                        <input type="number" name="cost" id="cost" value="{{ old('cost', $maintenance->cost) }}" min="0" step="0.01" 
                               class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('cost') border-red-500 @enderror">
                        @error('cost')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Доставчик на услугата -->
                    <div>
                        <label for="service_provider" class="block text-sm font-medium text-gray-700 mb-2">Доставчик на услугата</label>
                        <input type="text" name="service_provider" id="service_provider" value="{{ old('service_provider', $maintenance->service_provider) }}" 
                               class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('service_provider') border-red-500 @enderror">
                        @error('service_provider')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Следващо обслужване - дата -->
                    <div>
                        <label for="next_service_date" class="block text-sm font-medium text-gray-700 mb-2">Следващо обслужване - дата</label>
                        <input type="date" name="next_service_date" id="next_service_date" value="{{ old('next_service_date', $maintenance->next_service_date?->format('Y-m-d')) }}" 
                               class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('next_service_date') border-red-500 @enderror">
                        @error('next_service_date')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Следващо обслужване - километраж -->
                    <div>
                        <label for="next_service_mileage" class="block text-sm font-medium text-gray-700 mb-2">Следващо обслужване - километраж</label>
                        <input type="number" name="next_service_mileage" id="next_service_mileage" value="{{ old('next_service_mileage', $maintenance->next_service_mileage) }}" min="0" 
                               class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('next_service_mileage') border-red-500 @enderror">
                        @error('next_service_mileage')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Описание -->
                <div class="mt-6">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Описание</label>
                    <textarea name="description" id="description" rows="4" 
                              class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('description') border-red-500 @enderror">{{ old('description', $maintenance->description) }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Забележки -->
                <div class="mt-6">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">Забележки</label>
                    <textarea name="notes" id="notes" rows="3" 
                              class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 @error('notes') border-red-500 @enderror">{{ old('notes', $maintenance->notes) }}</textarea>
                    @error('notes')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Бутони -->
                <div class="mt-8 flex justify-end space-x-4">
                    <a href="{{ route('maintenances.show', $maintenance) }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        Отказ
                    </a>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Обнови
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
