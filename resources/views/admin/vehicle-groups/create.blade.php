@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-8">
    <x-breadcrumbs :items="[
        ['title' => 'Начало', 'url' => route('dashboard')],
        ['title' => 'Admin Panel', 'url' => route('admin.dashboard')],
        ['title' => 'Групи превозни средства', 'url' => route('admin.vehicle-groups.index')],
        ['title' => 'Създай група']
    ]" />

    <div class="max-w-2xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-gray-800">Създаване на група за превозни средства</h1>
            <a href="{{ route('admin.vehicle-groups.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Назад
            </a>
        </div>

        <div class="bg-white shadow-md rounded-lg p-6">
            <form action="{{ route('admin.vehicle-groups.store') }}" method="POST">
                @csrf

                <div class="space-y-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Име на групата *</label>
                        <input type="text" name="name" id="name" value="{{ old('name') }}" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('name') border-red-500 @enderror">
                        @error('name')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="owner_id" class="block text-sm font-medium text-gray-700 mb-2">Собственик *</label>
                        <select name="owner_id" id="owner_id" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('owner_id') border-red-500 @enderror">
                            <option value="">Изберете собственик</option>
                            @foreach($eligibleOwners as $owner)
                                <option value="{{ $owner->id }}" {{ old('owner_id') == $owner->id ? 'selected' : '' }}>
                                    {{ $owner->name }} ({{ $owner->email }}) - Администратор
                                </option>
                            @endforeach
                        </select>
                        @error('owner_id')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                        <p class="text-xs text-gray-500 mt-1">Собственикът трябва да има роля "Администратор"</p>
                    </div>
                </div>

                <!-- Info Box -->
                <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                    <h3 class="text-sm font-medium text-blue-700 mb-2">Информация за правата в групата:</h3>
                    <div class="text-xs text-blue-600 space-y-1">
                        <p><strong>Собственик (Admin):</strong> Пълни права над групата и превозните средства</p>
                        <p><strong>Мениджъри (Power User+):</strong> Могат да добавят/премахват превозни средства + всички права на членовете</p>
                        <p><strong>Членове (Regular User):</strong> Могат да добавят обслужвания + всички права на гостите</p>
                        <p><strong>Гости (Guest):</strong> Могат само да виждат превозните средства и обслужванията</p>
                        <p><strong>Външни потребители:</strong> Нямат достъп до превозните средства в групата</p>
                    </div>
                </div>

                <div class="mt-8 flex justify-end space-x-4">
                    <a href="{{ route('admin.vehicle-groups.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        Отказ
                    </a>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Създай група
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
