@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-8">
    <x-breadcrumbs :items="[
        ['title' => 'Начало', 'url' => route('dashboard')],
        ['title' => 'Admin Panel', 'url' => route('admin.dashboard')],
        ['title' => 'Групи превозни средства', 'url' => route('admin.vehicle-groups.index')],
        ['title' => $vehicleGroup->name]
    ]" />

    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-800">{{ $vehicleGroup->name }}</h1>
        <div class="space-x-2">
            <a href="{{ route('admin.vehicle-groups.edit', $vehicleGroup) }}" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Редактирай
            </a>
            <a href="{{ route('admin.vehicle-groups.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Назад
            </a>
        </div>
    </div>

    @if(session('success'))
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            {{ session('success') }}
        </div>
    @endif

    @if($errors->any())
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <ul>
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <!-- Основна информация -->
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">Основна информация</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-500">Име на групата</label>
                <p class="text-gray-900">{{ $vehicleGroup->name }}</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-500">Собственик</label>
                <p class="text-gray-900">{{ $vehicleGroup->owner->name ?? 'Няма собственик' }}</p>
                @if($vehicleGroup->owner)
                    <p class="text-sm text-gray-600">{{ $vehicleGroup->owner->email }}</p>
                @endif
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-500">Създадена на</label>
                <p class="text-gray-900">{{ $vehicleGroup->created_at->format('d.m.Y H:i') }}</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-500">Последна промяна</label>
                <p class="text-gray-900">{{ $vehicleGroup->updated_at->format('d.m.Y H:i') }}</p>
            </div>
        </div>
    </div>

    <!-- Мениджъри -->
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold text-gray-800">Мениджъри ({{ $vehicleGroup->managers->count() }})</h2>
        </div>

        @if($vehicleGroup->managers->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Име</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Email</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Роля</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Действия</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($vehicleGroup->managers as $manager)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $manager->name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $manager->email }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    @foreach($manager->roles as $role)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-1">
                                            {{ $role->name }}
                                        </span>
                                    @endforeach
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <form action="{{ route('admin.vehicle-groups.remove-member', [$vehicleGroup, $manager]) }}" method="POST" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-red-600 hover:text-red-900" onclick="return confirm('Сигурни ли сте?')">Премахни</button>
                                    </form>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <p class="text-gray-500">Няма мениджъри в тази група.</p>
        @endif

        <!-- Форма за добавяне на мениджър -->
        <div class="mt-4 p-4 bg-gray-50 rounded-lg">
            <h3 class="text-lg font-medium text-gray-800 mb-2">Добави мениджър</h3>
            <form action="{{ route('admin.vehicle-groups.add-member', $vehicleGroup) }}" method="POST" class="flex space-x-4">
                @csrf
                <input type="hidden" name="role_in_group" value="manager">
                <select name="user_id" required class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">Изберете потребител</option>
                    @foreach($availableUsers->filter(function($user) { return $user->hasRole(['power_user', 'admin']); }) as $user)
                        <option value="{{ $user->id }}">{{ $user->name }} ({{ $user->email }}) - 
                            @foreach($user->roles as $role){{ $role->name }}@if(!$loop->last), @endif @endforeach
                        </option>
                    @endforeach
                </select>
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Добави мениджър
                </button>
            </form>
            <p class="text-xs text-gray-500 mt-1">Мениджърите трябва да имат роля Power User или по-висока</p>
        </div>
    </div>

    <!-- Членове -->
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold text-gray-800">Членове ({{ $vehicleGroup->members->count() }})</h2>
        </div>

        @if($vehicleGroup->members->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Име</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Email</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Роля</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Действия</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($vehicleGroup->members as $member)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $member->name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $member->email }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    @foreach($member->roles as $role)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mr-1">
                                            {{ $role->name }}
                                        </span>
                                    @endforeach
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <form action="{{ route('admin.vehicle-groups.remove-member', [$vehicleGroup, $member]) }}" method="POST" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-red-600 hover:text-red-900" onclick="return confirm('Сигурни ли сте?')">Премахни</button>
                                    </form>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <p class="text-gray-500">Няма членове в тази група.</p>
        @endif

        <!-- Форма за добавяне на член -->
        <div class="mt-4 p-4 bg-gray-50 rounded-lg">
            <h3 class="text-lg font-medium text-gray-800 mb-2">Добави член</h3>
            <form action="{{ route('admin.vehicle-groups.add-member', $vehicleGroup) }}" method="POST" class="flex space-x-4">
                @csrf
                <input type="hidden" name="role_in_group" value="member">
                <select name="user_id" required class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">Изберете потребител</option>
                    @foreach($availableUsers as $user)
                        <option value="{{ $user->id }}">{{ $user->name }} ({{ $user->email }}) -
                            @foreach($user->roles as $role){{ $role->name }}@if(!$loop->last), @endif @endforeach
                        </option>
                    @endforeach
                </select>
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Добави член
                </button>
            </form>
            <p class="text-xs text-gray-500 mt-1">Членовете трябва да имат поне роля Guest</p>
        </div>
    </div>

    <!-- Превозни средства -->
    <div class="bg-white shadow-md rounded-lg p-6">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold text-gray-800">Превозни средства ({{ $vehicleGroup->vehicles->count() }})</h2>
        </div>

        @if($vehicleGroup->vehicles->count() > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Регистрационен номер</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Марка/Модел</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Тип</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Създател</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Действия</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($vehicleGroup->vehicles as $vehicle)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $vehicle->registration_number }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $vehicle->full_name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $vehicle->type_name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $vehicle->user->name ?? 'Неизвестен' }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="{{ route('vehicles.show', $vehicle) }}" class="text-indigo-600 hover:text-indigo-900 mr-3">Преглед</a>
                                    <form action="{{ route('admin.vehicle-groups.remove-vehicle', [$vehicleGroup, $vehicle]) }}" method="POST" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-red-600 hover:text-red-900" onclick="return confirm('Сигурни ли сте?')">Премахни</button>
                                    </form>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <p class="text-gray-500">Няма превозни средства в тази група.</p>
        @endif

        <!-- Форма за добавяне на превозно средство -->
        @if($availableVehicles->count() > 0)
        <div class="mt-4 p-4 bg-gray-50 rounded-lg">
            <h3 class="text-lg font-medium text-gray-800 mb-2">Добави превозно средство</h3>
            <form action="{{ route('admin.vehicle-groups.add-vehicle', $vehicleGroup) }}" method="POST" class="flex space-x-4">
                @csrf
                <select name="vehicle_id" required class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">Изберете превозно средство</option>
                    @foreach($availableVehicles as $vehicle)
                        <option value="{{ $vehicle->id }}">{{ $vehicle->registration_number }} - {{ $vehicle->full_name }}</option>
                    @endforeach
                </select>
                <button type="submit" class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                    Добави превозно средство
                </button>
            </form>
        </div>
        @endif
    </div>
</div>
@endsection
