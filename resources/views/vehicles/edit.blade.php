@extends('layouts.app')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-gray-800">Редактиране на превозно средство</h1>
            <a href="{{ route('vehicles.show', $vehicle) }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Назад
            </a>
        </div>

        <div class="bg-white shadow-md rounded-lg p-6">
            <form action="{{ route('vehicles.update', $vehicle) }}" method="POST">
                @csrf
                @method('PUT')

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Вид превозно средство *</label>
                        <select name="type" id="type"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('type') border-red-500 @enderror">
                            <option value="">Изберете тип превозно средство</option>
                            @foreach($vehicleTypes as $key => $label)
                                <option value="{{ $key }}" {{ old('type', $vehicle->type) === $key ? 'selected' : '' }}>
                                    {{ $label }}
                                </option>
                            @endforeach
                        </select>
                        @error('type')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="registration_number" class="block text-sm font-medium text-gray-700 mb-2">Регистрационен номер *</label>
                        <input type="text" name="registration_number" id="registration_number" value="{{ old('registration_number', $vehicle->registration_number) }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('registration_number') border-red-500 @enderror">
                        @error('registration_number')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="vin" class="block text-sm font-medium text-gray-700 mb-2">VIN номер *</label>
                        <input type="text" name="vin" id="vin" value="{{ old('vin', $vehicle->vin) }}" maxlength="17"
                               placeholder="17 символа (без I, O, Q)"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('vin') border-red-500 @enderror">
                        <p class="text-xs text-gray-500 mt-1">VIN номерът трябва да бъде точно 17 символа (букви и цифри, без I, O, Q)</p>
                        @error('vin')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="registration_date" class="block text-sm font-medium text-gray-700 mb-2">Дата на регистрация *</label>
                        <input type="date" name="registration_date" id="registration_date" value="{{ old('registration_date', $vehicle->registration_date->format('Y-m-d')) }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('registration_date') border-red-500 @enderror">
                        @error('registration_date')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="current_mileage" class="block text-sm font-medium text-gray-700 mb-2">Текущ километраж *</label>
                        <input type="number" name="current_mileage" id="current_mileage" value="{{ old('current_mileage', $vehicle->current_mileage) }}" min="{{ $vehicle->initial_mileage }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('current_mileage') border-red-500 @enderror">
                        <p class="text-xs text-gray-500 mt-1">Минимум: {{ number_format($vehicle->initial_mileage) }} км</p>
                        @error('current_mileage')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="make" class="block text-sm font-medium text-gray-700 mb-2">Марка</label>
                        <input type="text" name="make" id="make" value="{{ old('make', $vehicle->make) }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('make') border-red-500 @enderror">
                        @error('make')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="model" class="block text-sm font-medium text-gray-700 mb-2">Модел</label>
                        <input type="text" name="model" id="model" value="{{ old('model', $vehicle->model) }}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('model') border-red-500 @enderror">
                        @error('model')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="year" class="block text-sm font-medium text-gray-700 mb-2">Година на производство</label>
                        <input type="number" name="year" id="year" value="{{ old('year', $vehicle->year) }}" min="1900" max="{{ date('Y') + 1 }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('year') border-red-500 @enderror">
                        @error('year')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="color" class="block text-sm font-medium text-gray-700 mb-2">Цвят</label>
                        <input type="text" name="color" id="color" value="{{ old('color', $vehicle->color) }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('color') border-red-500 @enderror">
                        @error('color')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="group_id" class="block text-sm font-medium text-gray-700 mb-2">Група потребители</label>
                        <select name="group_id" id="group_id"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('group_id') border-red-500 @enderror">
                            <option value="">Без група (лично превозно средство)</option>
                            @foreach($groups as $group)
                                <option value="{{ $group->id }}" {{ old('group_id', $vehicle->group_id) == $group->id ? 'selected' : '' }}>
                                    {{ $group->name }}
                                    @if($group->owner)
                                        (Собственик: {{ $group->owner->name }})
                                    @endif
                                </option>
                            @endforeach
                        </select>
                        @error('group_id')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                        <p class="text-xs text-gray-500 mt-1">Изберете група, ако превозното средство ще се използва от група потребители</p>
                    </div>

                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" name="is_active" value="1" {{ old('is_active', $vehicle->is_active) ? 'checked' : '' }}
                                   class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-700">Активно превозно средство</span>
                        </label>
                    </div>
                </div>

                <div class="mt-6">
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">Забележки</label>
                    <textarea name="notes" id="notes" rows="4" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('notes') border-red-500 @enderror">{{ old('notes', $vehicle->notes) }}</textarea>
                    @error('notes')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div class="mt-8 flex justify-end space-x-4">
                    <a href="{{ route('vehicles.show', $vehicle) }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                        Отказ
                    </a>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Обнови превозно средство
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
